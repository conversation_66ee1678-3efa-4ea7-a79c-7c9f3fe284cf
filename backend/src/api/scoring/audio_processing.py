import json
import os
import re
import struct
import sys
import tempfile
from traceback import print_exc
from functools import lru_cache

import replicate
from flask import request, Response

from scoring import extract_midi_notes, NoteList, Note
from scoring.edit_distance import find_operations
from . import audio


@lru_cache(maxsize=16)
def load_notes(notes_id) -> NoteList:
    if os.environ.get("DEBUG") == "True":
        # Check if stored locally for convenience
        if os.path.exists(notes_path := f"data/{notes_id}.midi"):
            return extract_midi_notes(notes_path)

        if os.path.exists(notes_path := f"data/{notes_id}.pb"):
            with open(notes_path, 'rb') as f:
                notes = NoteList()
                notes.ParseFromString(f.read())
            return notes

    # TODO: Fetch from Appwrite
    raise NotImplementedError("Not implemented")


def parse_rep_output(replica, page_sizes) -> NoteList:
    """Convert Replicate output dict into a NoteList."""
    nl = NoteList()
    nl.size.extend(page_sizes)
    for ev in replica:
        note = Note(
            pitch=ev["pitch"],
            start_time=ev["start"],
            duration=ev["end"] - ev["start"],
            velocity=ev["velocity"],
            page=0,
            track=0
        )
        nl.notes.append(note)
    return nl


@audio.route('/receive', methods=['POST'])
def receive():
    audio_bytes = request.data
    if not audio_bytes:
        return {"error": "No audio data received"}, 400

    score_id = request.headers.get('X-Score-ID')
    notes_id = request.headers.get('X-Notes-ID')
    if not score_id:
        return {"error": "No score ID provided"}, 400

    try:
        print(f"Processing audio for score ID: {score_id}")

        # Determine test vs. production
        test_type = request.headers.get('X-Test-Type')
        is_test = bool(test_type and test_type != 'production')

        # Test file mapping
        test_cfg = {
            'spider_dance_played': {
                'played': "data/spider dance played.midi",
                'actual': "data/spiderdance_notes.pb"
            },
            'spider_dance_actual': {
                'played': "data/spider dance actual.midi",
                'actual': "data/spiderdance_notes.pb"
            },
        }

        if is_test:
            cfg = test_cfg.get(test_type, test_cfg['spider_dance_played'])
            print(f"Using test config: {test_type}")
            played_notes = extract_midi_notes(cfg['played'])
            actual_notes = load_notes(cfg['actual'])
        else:
            print("Using replicate.run for audio processing")

            if not notes_id:
                return {"error": "No notes ID provided"}, 400

            # Auto-detect audio format by inspecting magic bytes
            header = audio_bytes[:12]
            if header[:4] == b'\x1A\x45\xDF\xA3':
                ext = '.webm'
            elif header.startswith(b'RIFF') and header[8:12] == b'WAVE':
                ext = '.wav'
            elif header[:3] == b'ID3' or audio_bytes[0] == 0xFF:
                ext = '.mp3'
            elif header.startswith(b'OggS'):
                ext = '.ogg'
            elif header.startswith(b'fLaC'):
                ext = '.flac'
            else:
                ext = '.bin'

            # Dump incoming bytes to a file
            if os.environ.get("DEBUG") == "True":
                tmp_path = f"data/last_audio{ext}"
                with open(tmp_path, 'wb') as f:
                    f.write(audio_bytes)
            else:
                fd, tmp_path = tempfile.mkstemp(suffix=ext)
                with os.fdopen(fd, 'wb') as tmp:
                    tmp.write(audio_bytes)

            # Model version from env
            model_version = os.environ.get("TRANSKUN_VERSION")
            if not model_version:
                raise ValueError("TRANSKUN_VERSION not set in environment")

            # Load ground truth NoteList (could be mapped per score_id)
            actual_notes = load_notes(notes_id)

            # Run the model
            # output = replicate.run(
            #     model_version,
            #     input={"audio": open(tmp_path, "rb")},
            #     use_file_output=False
            # )
            output = None

            if output is None:
                output = []

            if os.environ.get("DEBUG") != "True":
                os.unlink(tmp_path)

            # Handle JSON vs. native dict
            rep_out = json.loads(output) if isinstance(output, str) else output
            played_notes = parse_rep_output(rep_out, actual_notes.size)

        # Compute edit operations
        ops, aligned_idx = find_operations(actual_notes.notes, played_notes.notes)
        ops.size.extend(actual_notes.size)
        print("Operations:", ops.edits[:20])
        print("Sizes:", len(played_notes.notes), len(actual_notes.notes))
        print("Aligned indices:", aligned_idx[:20])

        # Optional debug dump
        if os.environ.get("DEBUG") == "True":
            def _join(m): return " ".join(m.group().split())
            with open('data/last_edits.json', 'w') as f:
                j = json.dumps(aligned_idx, ensure_ascii=False, indent=4)
                f.write(re.sub(r"(?<=\[)[^\[\]]+(?=])", _join, j))

        # Build response NoteList
        if is_test:
            response_nl = NoteList()
            response_nl.notes.extend(played_notes.notes)
            response_nl.size.extend(actual_notes.size)
        else:
            response_nl = played_notes.notes

        # Serialize EditList and NoteList with length prefix
        edit_bytes = ops.SerializeToString()
        notes_bytes = response_nl.SerializeToString()
        payload = struct.pack('>I', len(edit_bytes)) + edit_bytes + notes_bytes
        print(f"Serialized payload size: {len(payload)} bytes")

        # Return protobuf binary
        res = Response(payload, mimetype='application/protobuf')
        res.headers.update({
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Response-Format': 'combined'
        })
        return res

    except Exception as e:
        print_exc()
        err = f"Error processing audio: {e}"
        print(f"ERROR: {err}", file=sys.stderr)
        return {"error": err}, 400
